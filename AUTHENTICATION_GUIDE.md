# 🔐 Django-WebSocket Authentication Integration Guide

## Overview

This guide explains how to integrate Django authentication with your WebSocket collaboration server for secure multi-user real-time editing.

## 🏗️ Architecture

```
┌─────────────────┐    JWT Token    ┌──────────────────────┐
│   Django App    │ ──────────────► │  WebSocket Server   │
│   (WSGI)        │                 │   (Node.js)          │
└─────────────────┘                 └──────────────────────┘
         │                                      │
         │                                      │
         ▼                                      ▼
┌─────────────────┐                 ┌──────────────────────┐
│   PostgreSQL    │                 │       Redis          │
│   (User Data)   │                 │   (Session Cache)    │
└─────────────────┘                 └──────────────────────┘
```

## 🚀 Quick Start

### 1. Start the WebSocket Server

```bash
# Install dependencies
npm install

# Start the server
npm start
```

### 2. Set up Django (Example Implementation)

```bash
# Install Django dependencies
pip install djangorestframework djangorestframework-simplejwt redis

# Add to Django settings.py
INSTALLED_APPS = [
    'rest_framework',
    'rest_framework_simplejwt',
]

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
}

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=24),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'SIGNING_KEY': 'your-super-secret-jwt-key-change-in-production',
}
```

### 3. Test Authentication Flow

1. **Get JWT Token from Django:**
   ```bash
   curl -X POST http://localhost:8000/api/auth/token/ \
        -H "Content-Type: application/json" \
        -d '{"username": "your_username", "password": "your_password"}'
   ```

2. **Use Token in WebSocket Connection:**
   ```javascript
   const wsUrl = `ws://localhost:3000/document-id?token=${jwtToken}`;
   const provider = new WebsocketProvider(wsUrl, documentId, doc);
   ```

## 🔧 Configuration

### Environment Variables

Create a `.env` file with the following variables:

```env
# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h

# Django Integration
DJANGO_API_URL=http://localhost:8000
DJANGO_API_TIMEOUT=5000

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_KEY_PREFIX=collab:

# Security Settings
MAX_CONNECTIONS_PER_USER=5
RATE_LIMIT_WINDOW=60000
RATE_LIMIT_MAX=100
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8000
```

## 🔒 Security Features

### 1. JWT Token Validation
- Validates JWT signature using shared secret
- Checks token expiration
- Extracts user information from token payload

### 2. User Verification
- Verifies user exists and is active in Django
- Caches user data in Redis for performance
- Checks user permissions and groups

### 3. Document Access Control
- Validates user access to specific documents
- Supports role-based permissions (admin, editor, viewer)
- Caches access decisions for performance

### 4. Session Management
- Tracks active user sessions
- Manages connection limits per user
- Handles graceful disconnection cleanup

## 📡 API Endpoints

### WebSocket Server Endpoints

- `GET /health` - Health check
- `GET /api/stats` - Server statistics
- `GET /api/auth/status` - Authentication status check

### Django Endpoints (to implement)

- `POST /api/auth/token/` - Get JWT token
- `GET /api/auth/verify-user/` - Verify user for WebSocket
- `GET /api/documents/{id}/check-access/` - Check document access
- `GET /api/auth/user-permissions/` - Get user permissions

## 🧪 Testing

### 1. Manual Testing

1. **Start Services:**
   ```bash
   # Terminal 1: Start WebSocket server
   npm start
   
   # Terminal 2: Start Django server
   python manage.py runserver
   ```

2. **Test Authentication:**
   - Visit `http://localhost:3000/auth-demo.html`
   - Enter Django credentials
   - Get JWT token
   - Use token in collaboration editor

### 2. Automated Testing

```bash
# Test WebSocket server
npm test

# Test Django integration
python manage.py test
```

## 🐳 Docker Deployment

### Development Setup

```bash
# Use the authentication-enabled Makefile
make -f Makefile.auth dev-setup
```

### Production Deployment

```bash
# Set production environment
export NODE_ENV=production

# Deploy with authentication
make -f Makefile.auth prod-deploy
```

## 🔍 Troubleshooting

### Common Issues

1. **JWT Token Invalid:**
   - Check JWT secret matches between Django and WebSocket server
   - Verify token hasn't expired
   - Ensure token format is correct

2. **Connection Refused:**
   - Check if Django API is accessible from WebSocket server
   - Verify CORS settings allow WebSocket server origin
   - Check network connectivity between services

3. **Redis Connection Failed:**
   - Ensure Redis is running and accessible
   - Check Redis URL configuration
   - Verify Redis authentication if enabled

4. **Document Access Denied:**
   - Check user permissions in Django
   - Verify document exists and user has access
   - Check access control logic implementation

### Debug Mode

Enable debug logging:

```env
LOG_LEVEL=debug
ENABLE_AUTH_LOGS=true
ENABLE_ACCESS_LOGS=true
```

## 📊 Monitoring

### Health Checks

```bash
# WebSocket server health
curl http://localhost:3000/health

# Django health (implement this endpoint)
curl http://localhost:8000/health/

# Redis health
redis-cli ping
```

### Metrics

The WebSocket server provides real-time metrics:

- Active connections count
- Active users count
- Document session statistics
- Authentication success/failure rates

## 🔐 Security Best Practices

1. **JWT Secrets:**
   - Use strong, unique secrets for production
   - Rotate secrets regularly
   - Never commit secrets to version control

2. **HTTPS/WSS:**
   - Use HTTPS for Django in production
   - Use WSS for WebSocket connections in production
   - Implement proper SSL certificate management

3. **Rate Limiting:**
   - Enable rate limiting for authentication endpoints
   - Limit connections per user
   - Implement IP-based rate limiting

4. **Input Validation:**
   - Validate all user inputs
   - Sanitize document IDs and user data
   - Implement proper error handling

## 🎯 Next Steps

1. **Implement Django Views:**
   - Use the provided `django_integration_example.py` as a template
   - Customize authentication logic for your use case
   - Add proper error handling and logging

2. **Enhance Security:**
   - Implement token refresh mechanism
   - Add audit logging for security events
   - Set up monitoring and alerting

3. **Scale for Production:**
   - Use Redis Cluster for high availability
   - Implement load balancing for WebSocket servers
   - Set up proper monitoring and logging

4. **Add Features:**
   - Document locking mechanism
   - User presence indicators
   - Real-time notifications
   - File upload/attachment support

## 📚 Additional Resources

- [JWT.io](https://jwt.io/) - JWT token debugger
- [Django REST Framework](https://www.django-rest-framework.org/) - API framework
- [YJS Documentation](https://docs.yjs.dev/) - Collaborative editing
- [Tiptap Documentation](https://tiptap.dev/) - Rich text editor

## 🏆 Success Criteria

Your authentication integration is successful when:

- ✅ Users can authenticate with Django credentials
- ✅ JWT tokens are properly validated
- ✅ Document access is controlled by permissions
- ✅ Real-time collaboration works with authenticated users
- ✅ Sessions are properly managed and cleaned up
- ✅ System is secure and production-ready

---

**🎉 Congratulations!** You now have a secure, production-ready collaborative editing system with Django authentication integration!
