# Makefile Documentation - Realtime YJS Server

This document provides comprehensive information about the Makefile for managing your Realtime YJS Server Docker setup.

## 🚀 Quick Start

```bash
# Build and run the application
make rebuild

# Or step by step
make build
make run

# Check status
make status

# View logs
make logs
```

## 📋 Available Commands

### Core Commands (As Requested)

#### `make build`
Builds the Docker image using cache for faster builds.
```bash
make build
```

#### `make run`
Runs the Docker container with proper configuration.
```bash
make run
```

#### `make rebuild`
Rebuilds the image and runs the container (combines `build` + `run`).
```bash
make rebuild
```

#### `make rebuild-no-cache`
Rebuilds the image without cache and runs the container.
```bash
make rebuild-no-cache
```

### Additional Build Commands

#### `make build-no-cache`
Builds the Docker image without using cache (fresh build).
```bash
make build-no-cache
```

### Container Management

#### `make start`
Starts an existing stopped container.
```bash
make start
```

#### `make stop`
Stops the running container.
```bash
make stop
```

#### `make restart`
Restarts the container.
```bash
make restart
```

#### `make clean`
Stops and removes the container.
```bash
make clean
```

#### `make remove`
Removes the container (without stopping first).
```bash
make remove
```

### Monitoring & Debugging

#### `make status`
Shows container status and resource usage.
```bash
make status
```

#### `make logs`
Displays container logs.
```bash
make logs
```

#### `make logs-follow`
Follows container logs in real-time.
```bash
make logs-follow
```

#### `make health`
Checks application health endpoint.
```bash
make health
```

#### `make shell`
Opens an interactive shell inside the running container.
```bash
make shell
```

### Docker Compose Commands

#### `make compose-up`
Starts services using Docker Compose.
```bash
make compose-up
```

#### `make compose-up-prod`
Starts services with production profile (includes nginx).
```bash
make compose-up-prod
```

#### `make compose-down`
Stops Docker Compose services.
```bash
make compose-down
```

#### `make compose-logs`
Shows Docker Compose logs.
```bash
make compose-logs
```

### Development & Testing

#### `make dev`
Runs the container in development mode (foreground).
```bash
make dev
```

#### `make run-foreground`
Runs the container in foreground mode.
```bash
make run-foreground
```

#### `make test`
Tests the complete Docker setup.
```bash
make test
```

### Cleanup Commands

#### `make clean-all`
Removes both container and image.
```bash
make clean-all
```

#### `make prune`
Removes unused Docker resources.
```bash
make prune
```

### Help

#### `make help` (default)
Shows available commands and usage examples.
```bash
make help
# or just
make
```

## ⚙️ Configuration

### Environment Variables
The Makefile uses these default values:
- **IMAGE_NAME**: `realtime-yjs-server`
- **CONTAINER_NAME**: `realtime-yjs-server`
- **PORT**: `3000`
- **ENV_FILE**: `.env.docker`

### Customizing Configuration
You can override these values by setting environment variables:
```bash
# Use a different port
PORT=8080 make run

# Use a different container name
CONTAINER_NAME=my-yjs-server make run

# Use a different environment file
ENV_FILE=.env.production make run
```

## 🔄 Common Workflows

### Development Workflow
```bash
# Start development
make dev

# In another terminal, check logs
make logs-follow

# Make changes to code, then rebuild
make rebuild

# Check health
make health
```

### Production Deployment
```bash
# Build without cache for clean deployment
make build-no-cache

# Run the container
make run

# Check status
make status

# Monitor health
make health
```

### Debugging Issues
```bash
# Check container status
make status

# View logs
make logs

# Check application health
make health

# Access container shell
make shell

# Clean up and restart
make clean
make rebuild
```

### Testing Changes
```bash
# Test the complete setup
make test

# Or manual testing
make rebuild
make health
make clean
```

## 🎨 Features

### Colored Output
The Makefile provides colored output for better readability:
- **Blue**: Informational messages
- **Green**: Success messages
- **Yellow**: Warning messages
- **Red**: Error messages

### Smart Container Management
- Automatically stops and removes existing containers before running new ones
- Checks for container existence before operations
- Provides helpful error messages

### Environment File Support
- Automatically uses `.env.docker` if available
- Falls back to default environment if file doesn't exist
- Supports custom environment files

### Volume Mounting
- Automatically mounts logs directory for persistent logging
- Supports custom volume configurations

### Health Checking
- Built-in health check functionality
- JSON output formatting when available
- Port detection and validation

## 🔧 Troubleshooting

### Common Issues

#### "Container already exists"
This is normal - the Makefile automatically handles this by stopping and removing existing containers.

#### "Docker is not running"
Start Docker Desktop or Docker daemon before running make commands.

#### "Port already in use"
```bash
# Check what's using the port
lsof -i :3000

# Use a different port
PORT=8080 make run
```

#### "Environment file not found"
This is a warning, not an error. The container will use default environment variables.

### Debug Commands
```bash
# Check Docker status
docker info

# List all containers
docker ps -a

# List all images
docker images

# Check container logs
make logs

# Access container shell
make shell
```

## 📊 Examples

### Basic Usage
```bash
# Build and run
make rebuild

# Check if it's working
make health
```

### Custom Configuration
```bash
# Run on different port with custom env file
PORT=8080 ENV_FILE=.env.production make run
```

### Development Mode
```bash
# Run in foreground for development
make dev
```

### Production Deployment
```bash
# Clean build and run
make clean-all
make build-no-cache
make run
make health
```

### Monitoring
```bash
# Check status and follow logs
make status
make logs-follow
```

## 🎯 Best Practices

1. **Always check health** after starting: `make health`
2. **Use rebuild for development**: `make rebuild`
3. **Use rebuild-no-cache for production**: `make rebuild-no-cache`
4. **Monitor logs during development**: `make logs-follow`
5. **Clean up when done**: `make clean`
6. **Test the complete setup**: `make test`

## 🚀 Integration

The Makefile integrates seamlessly with:
- **Docker scripts**: `docker-run.sh` and `docker-manage.sh`
- **Docker Compose**: Built-in compose commands
- **CI/CD pipelines**: All commands return proper exit codes
- **Development workflows**: Foreground and background modes

Your Makefile is now ready for production use! 🎉
