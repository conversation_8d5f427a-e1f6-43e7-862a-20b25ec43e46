# Makefile for Django + WebSocket Collaboration Server with Authentication
# Usage: make -f Makefile.auth <command>

.PHONY: help build run stop clean logs shell test setup-env

# Default target
help:
	@echo "🚀 Django + WebSocket Collaboration Server with Authentication"
	@echo ""
	@echo "Available commands:"
	@echo "  setup-env     - Create environment files from examples"
	@echo "  build         - Build all Docker containers"
	@echo "  run           - Start all services"
	@echo "  stop          - Stop all services"
	@echo "  restart       - Restart all services"
	@echo "  clean         - Remove containers and volumes"
	@echo "  logs          - Show logs from all services"
	@echo "  logs-django   - Show Django logs"
	@echo "  logs-ws       - Show WebSocket server logs"
	@echo "  logs-redis    - Show Redis logs"
	@echo "  shell-django  - Open Django shell"
	@echo "  shell-ws      - Open WebSocket server shell"
	@echo "  test          - Run tests"
	@echo "  migrate       - Run Django migrations"
	@echo "  createsuperuser - Create Django superuser"
	@echo "  collectstatic - Collect Django static files"
	@echo "  backup        - Backup database"
	@echo "  restore       - Restore database from backup"
	@echo "  status        - Show service status"
	@echo ""

# Environment setup
setup-env:
	@echo "📝 Setting up environment files..."
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "✅ Created .env from .env.example"; \
		echo "⚠️  Please edit .env with your configuration"; \
	else \
		echo "ℹ️  .env already exists"; \
	fi
	@if [ ! -f django_app/.env ]; then \
		echo "Creating django_app/.env..."; \
		mkdir -p django_app; \
		echo "DEBUG=True" > django_app/.env; \
		echo "SECRET_KEY=your-django-secret-key-change-in-production" >> django_app/.env; \
		echo "DATABASE_URL=***********************************************************************/collaboration_db" >> django_app/.env; \
		echo "REDIS_URL=redis://:redis_password_change_me@redis:6379/0" >> django_app/.env; \
		echo "JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production" >> django_app/.env; \
		echo "✅ Created django_app/.env"; \
	else \
		echo "ℹ️  django_app/.env already exists"; \
	fi

# Build containers
build:
	@echo "🔨 Building Docker containers..."
	docker-compose -f docker-compose.auth.yml build --no-cache

# Start services
run: setup-env
	@echo "🚀 Starting all services..."
	docker-compose -f docker-compose.auth.yml up -d
	@echo "✅ Services started!"
	@echo ""
	@echo "🌐 Services available at:"
	@echo "  Django:          http://localhost:8000"
	@echo "  WebSocket:       http://localhost:3000"
	@echo "  Auth Demo:       http://localhost:3000/auth-demo.html"
	@echo "  Collaboration:   http://localhost:3000/"
	@echo ""
	@echo "📊 Check status with: make -f Makefile.auth status"

# Stop services
stop:
	@echo "🛑 Stopping all services..."
	docker-compose -f docker-compose.auth.yml down

# Restart services
restart: stop run

# Clean up everything
clean:
	@echo "🧹 Cleaning up containers and volumes..."
	docker-compose -f docker-compose.auth.yml down -v --remove-orphans
	docker system prune -f
	@echo "✅ Cleanup complete!"

# Show logs
logs:
	docker-compose -f docker-compose.auth.yml logs -f

logs-django:
	docker-compose -f docker-compose.auth.yml logs -f django

logs-ws:
	docker-compose -f docker-compose.auth.yml logs -f websocket

logs-redis:
	docker-compose -f docker-compose.auth.yml logs -f redis

logs-postgres:
	docker-compose -f docker-compose.auth.yml logs -f postgres

# Shell access
shell-django:
	docker-compose -f docker-compose.auth.yml exec django python manage.py shell

shell-ws:
	docker-compose -f docker-compose.auth.yml exec websocket sh

shell-redis:
	docker-compose -f docker-compose.auth.yml exec redis redis-cli

shell-postgres:
	docker-compose -f docker-compose.auth.yml exec postgres psql -U collaboration_user -d collaboration_db

# Django management
migrate:
	@echo "🔄 Running Django migrations..."
	docker-compose -f docker-compose.auth.yml exec django python manage.py migrate

createsuperuser:
	@echo "👤 Creating Django superuser..."
	docker-compose -f docker-compose.auth.yml exec django python manage.py createsuperuser

collectstatic:
	@echo "📦 Collecting static files..."
	docker-compose -f docker-compose.auth.yml exec django python manage.py collectstatic --noinput

# Testing
test:
	@echo "🧪 Running tests..."
	docker-compose -f docker-compose.auth.yml exec django python manage.py test
	docker-compose -f docker-compose.auth.yml exec websocket npm test

# Database operations
backup:
	@echo "💾 Creating database backup..."
	@mkdir -p backups
	docker-compose -f docker-compose.auth.yml exec postgres pg_dump -U collaboration_user collaboration_db > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "✅ Backup created in backups/ directory"

restore:
	@echo "📥 Restoring database from backup..."
	@if [ -z "$(BACKUP_FILE)" ]; then \
		echo "❌ Please specify BACKUP_FILE=path/to/backup.sql"; \
		exit 1; \
	fi
	docker-compose -f docker-compose.auth.yml exec -T postgres psql -U collaboration_user -d collaboration_db < $(BACKUP_FILE)
	@echo "✅ Database restored from $(BACKUP_FILE)"

# Service status
status:
	@echo "📊 Service Status:"
	@echo ""
	docker-compose -f docker-compose.auth.yml ps
	@echo ""
	@echo "🔍 Health Checks:"
	@echo "Django:    $$(curl -s -o /dev/null -w '%{http_code}' http://localhost:8000/health/ 2>/dev/null || echo 'DOWN')"
	@echo "WebSocket: $$(curl -s -o /dev/null -w '%{http_code}' http://localhost:3000/health 2>/dev/null || echo 'DOWN')"
	@echo "Redis:     $$(docker-compose -f docker-compose.auth.yml exec redis redis-cli ping 2>/dev/null || echo 'DOWN')"
	@echo "Postgres:  $$(docker-compose -f docker-compose.auth.yml exec postgres pg_isready -U collaboration_user 2>/dev/null | grep -q 'accepting' && echo 'UP' || echo 'DOWN')"

# Development helpers
dev-setup: setup-env build run migrate createsuperuser
	@echo "🎉 Development environment setup complete!"
	@echo ""
	@echo "Next steps:"
	@echo "1. Visit http://localhost:8000/admin to access Django admin"
	@echo "2. Visit http://localhost:3000/auth-demo.html to get JWT tokens"
	@echo "3. Visit http://localhost:3000/ to test collaboration"

# Production deployment
prod-deploy:
	@echo "🚀 Deploying to production..."
	@if [ "$(NODE_ENV)" != "production" ]; then \
		echo "❌ NODE_ENV must be set to 'production'"; \
		exit 1; \
	fi
	docker-compose -f docker-compose.auth.yml -f docker-compose.prod.yml up -d --build
	@echo "✅ Production deployment complete!"

# Monitoring
monitor:
	@echo "📊 System Monitoring:"
	@echo ""
	@echo "Container Resource Usage:"
	docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
	@echo ""
	@echo "Disk Usage:"
	docker system df
	@echo ""
	@echo "Network Status:"
	docker network ls | grep collaboration

# Security scan
security-scan:
	@echo "🔒 Running security scans..."
	@if command -v trivy >/dev/null 2>&1; then \
		trivy image collaboration_django; \
		trivy image collaboration_websocket; \
	else \
		echo "⚠️  Trivy not installed. Install with: brew install trivy"; \
	fi

# Update dependencies
update-deps:
	@echo "📦 Updating dependencies..."
	docker-compose -f docker-compose.auth.yml exec django pip list --outdated
	docker-compose -f docker-compose.auth.yml exec websocket npm outdated

# Quick commands for common operations
quick-restart-ws:
	docker-compose -f docker-compose.auth.yml restart websocket

quick-restart-django:
	docker-compose -f docker-compose.auth.yml restart django

# Generate JWT secret
generate-jwt-secret:
	@echo "🔑 Generated JWT Secret:"
	@openssl rand -base64 64 | tr -d '\n' && echo

# Show configuration
show-config:
	@echo "⚙️  Current Configuration:"
	@echo ""
	@echo "Environment files:"
	@ls -la .env* 2>/dev/null || echo "No .env files found"
	@echo ""
	@echo "Docker compose files:"
	@ls -la docker-compose*.yml 2>/dev/null || echo "No docker-compose files found"
