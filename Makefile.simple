# Simplified Makefile for WebSocket Collaboration Server with Authentication
# This version works with your existing setup + Redis for caching

.PHONY: help build run stop clean logs shell test setup-env

# Default target
help:
	@echo "🚀 WebSocket Collaboration Server with Authentication"
	@echo ""
	@echo "Available commands:"
	@echo "  setup-env     - Create environment files"
	@echo "  build         - Build WebSocket server container"
	@echo "  run           - Start WebSocket server + Redis"
	@echo "  run-local     - Start server locally (no Docker)"
	@echo "  stop          - Stop all services"
	@echo "  restart       - Restart all services"
	@echo "  clean         - Remove containers and volumes"
	@echo "  logs          - Show logs from all services"
	@echo "  logs-ws       - Show WebSocket server logs"
	@echo "  logs-redis    - Show Redis logs"
	@echo "  shell-ws      - Open WebSocket server shell"
	@echo "  shell-redis   - Open Redis shell"
	@echo "  status        - Show service status"
	@echo "  test          - Run tests"
	@echo ""

# Environment setup
setup-env:
	@echo "📝 Setting up environment files..."
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "✅ Created .env from .env.example"; \
		echo "⚠️  Please edit .env with your configuration"; \
	else \
		echo "ℹ️  .env already exists"; \
	fi

# Build containers
build:
	@echo "🔨 Building WebSocket server container..."
	docker-compose -f docker-compose.simple.yml build --no-cache

# Start services with Docker
run: setup-env
	@echo "🚀 Starting WebSocket server + Redis..."
	docker-compose -f docker-compose.simple.yml up -d
	@echo "✅ Services started!"
	@echo ""
	@echo "🌐 Services available at:"
	@echo "  WebSocket Server:    http://localhost:3000"
	@echo "  Auth Demo:           http://localhost:3000/auth-demo.html"
	@echo "  Collaboration:       http://localhost:3000/"
	@echo "  Redis:               localhost:6379"
	@echo ""
	@echo "📊 Check status with: make -f Makefile.simple status"

# Start server locally (no Docker)
run-local: setup-env
	@echo "🚀 Starting WebSocket server locally..."
	@echo "⚠️  Make sure Redis is running locally on port 6379"
	@echo "⚠️  Make sure Django is running on port 8000 (if using authentication)"
	npm start

# Stop services
stop:
	@echo "🛑 Stopping all services..."
	docker-compose -f docker-compose.simple.yml down

# Restart services
restart: stop run

# Clean up everything
clean:
	@echo "🧹 Cleaning up containers and volumes..."
	docker-compose -f docker-compose.simple.yml down -v --remove-orphans
	docker system prune -f
	@echo "✅ Cleanup complete!"

# Show logs
logs:
	docker-compose -f docker-compose.simple.yml logs -f

logs-ws:
	docker-compose -f docker-compose.simple.yml logs -f websocket

logs-redis:
	docker-compose -f docker-compose.simple.yml logs -f redis

# Shell access
shell-ws:
	docker-compose -f docker-compose.simple.yml exec websocket sh

shell-redis:
	docker-compose -f docker-compose.simple.yml exec redis redis-cli

# Testing
test:
	@echo "🧪 Running tests..."
	npm test

# Service status
status:
	@echo "📊 Service Status:"
	@echo ""
	docker-compose -f docker-compose.simple.yml ps
	@echo ""
	@echo "🔍 Health Checks:"
	@echo "WebSocket: $$(curl -s -o /dev/null -w '%{http_code}' http://localhost:3000/health 2>/dev/null || echo 'DOWN')"
	@echo "Redis:     $$(docker-compose -f docker-compose.simple.yml exec redis redis-cli ping 2>/dev/null || echo 'DOWN')"

# Development setup
dev-setup: setup-env build run
	@echo "🎉 Development environment setup complete!"
	@echo ""
	@echo "Next steps:"
	@echo "1. Start your Django server: python manage.py runserver"
	@echo "2. Visit http://localhost:3000/auth-demo.html to test authentication"
	@echo "3. Visit http://localhost:3000/ to test collaboration"
	@echo ""
	@echo "📝 Note: For full authentication, you need Django running on port 8000"

# Quick commands
quick-restart-ws:
	docker-compose -f docker-compose.simple.yml restart websocket

# Install dependencies
install:
	@echo "📦 Installing dependencies..."
	npm install

# Build bundle
build-bundle:
	@echo "🔨 Building Tiptap bundle..."
	npm run build:bundle:dev

# Generate JWT secret
generate-jwt-secret:
	@echo "🔑 Generated JWT Secret:"
	@openssl rand -base64 64 | tr -d '\n' && echo

# Show configuration
show-config:
	@echo "⚙️  Current Configuration:"
	@echo ""
	@echo "Environment files:"
	@ls -la .env* 2>/dev/null || echo "No .env files found"
	@echo ""
	@echo "Docker compose files:"
	@ls -la docker-compose*.yml 2>/dev/null || echo "No docker-compose files found"
	@echo ""
	@echo "Package.json scripts:"
	@grep -A 10 '"scripts"' package.json 2>/dev/null || echo "No package.json found"

# Development workflow
dev: install build-bundle run-local

# Production workflow  
prod: setup-env build run

# Monitor resources
monitor:
	@echo "📊 System Monitoring:"
	@echo ""
	@echo "Container Resource Usage:"
	docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" 2>/dev/null || echo "No containers running"
	@echo ""
	@echo "Disk Usage:"
	docker system df 2>/dev/null || echo "Docker not available"

# Backup Redis data
backup-redis:
	@echo "💾 Creating Redis backup..."
	@mkdir -p backups
	docker-compose -f docker-compose.simple.yml exec redis redis-cli --rdb /data/backup.rdb
	docker cp collaboration_redis:/data/backup.rdb backups/redis_backup_$(shell date +%Y%m%d_%H%M%S).rdb
	@echo "✅ Redis backup created in backups/ directory"
