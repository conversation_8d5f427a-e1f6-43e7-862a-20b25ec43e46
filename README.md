# Tiptap Collaborative Editor Server

A production-ready, real-time collaborative rich text editor server built with YJS, y-websocket, and Tiptap, following SOLID principles and best practices.

## Features

- ✨ **Rich Text Editing**: Professional Tiptap editor with formatting, lists, headings
- 🚀 **Real-time Collaboration**: Built on YJS for conflict-free replicated data types (CRDTs)
- 👥 **Collaborative Cursors**: See other users' cursors and selections in real-time
- 🌐 **Y-WebSocket Protocol**: Native WebSocket connections for optimal performance
- ⌨️ **Markdown Shortcuts**: Type `# ` for headings, `- ` for lists, `> ` for quotes
- 🏗️ **SOLID Architecture**: Clean, maintainable code following SOLID principles
- 🐳 **Docker Ready**: Fully containerized with Docker and docker-compose
- 📊 **Monitoring**: Built-in health checks and statistics endpoints
- 🔒 **Security**: Helmet.js, CORS, rate limiting, and non-root Docker user
- 📝 **Logging**: Comprehensive logging with Winston
- ⚡ **Performance**: Efficient connection and document management
- 🔄 **Graceful Shutdown**: Proper cleanup and client notification

## Architecture

The server follows SOLID principles with a clean separation of concerns:

```
src/
├── config/          # Configuration management
├── interfaces/      # Interface definitions (ISP)
├── managers/        # Core business logic managers
├── handlers/        # WebSocket event handlers
├── services/        # High-level service orchestration
├── server/          # Express server setup
├── middleware/      # Error handling middleware
└── utils/           # Utility classes (Logger)
```

### SOLID Principles Implementation

- **Single Responsibility**: Each class has one reason to change
- **Open/Closed**: Extensible without modification
- **Liskov Substitution**: Interfaces ensure substitutability
- **Interface Segregation**: Focused, specific interfaces
- **Dependency Inversion**: Depends on abstractions, not concretions

## Available Endpoints

The server provides a clean, minimal set of endpoints focused on the Tiptap collaborative editor:

- **`GET /`** - Main Tiptap collaborative editor interface
- **`GET /health`** - Server health check
- **`GET /api/stats`** - Server statistics and connection info
- **`GET /api/documents/:documentId`** - Document information
- **`DELETE /api/documents/:documentId`** - Force document cleanup (admin)
- **`WebSocket /[documentId]`** - Real-time collaboration WebSocket

**Note**: Old example endpoints (like `/examples/y-websocket-client.html`) have been removed for security and clarity. The server now only serves the essential Tiptap collaborative editor.

## Quick Start

### Using Docker (Recommended)

1. **Clone and setup**:
   ```bash
   git clone <repository-url>
   cd realtime-yjs-server
   cp .env.example .env
   ```

2. **Run with Docker Compose**:
   ```bash
   docker-compose up -d
   ```

3. **Check health**:
   ```bash
   curl http://localhost:3000/health
   ```

4. **Open the editor**:
   ```bash
   open http://localhost:3000
   ```

### Manual Installation

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

3. **Start the server**:
   ```bash
   npm start
   # or for development
   npm run dev
   ```

### Clean Restart

For a completely fresh start (kills all processes, cleans logs, removes containers):

```bash
./restart-server.sh
```

This script will:
- Kill any existing Node.js processes
- Stop and remove Docker containers
- Clear log files
- Start the server fresh

## Configuration

Environment variables (see `.env.example`):

| Variable | Default | Description |
|----------|---------|-------------|
| `PORT` | 3000 | Server port |
| `HOST` | 0.0.0.0 | Server host |
| `NODE_ENV` | development | Environment |
| `CORS_ORIGIN` | * | CORS origin |
| `LOG_LEVEL` | info | Logging level |
| `YJS_PERSISTENCE` | false | Enable YJS persistence |
| `YJS_GC_ENABLED` | true | Enable garbage collection |
| `WS_PING_TIMEOUT` | 60000 | WebSocket ping timeout |
| `WS_PING_INTERVAL` | 25000 | WebSocket ping interval |

## API Endpoints

### Health Check
```
GET /health
```

### Statistics
```
GET /api/stats
```

### Document Information
```
GET /api/documents/:documentId
```

### Force Document Cleanup
```
DELETE /api/documents/:documentId
```

## Y-WebSocket Protocol

The server uses the standard y-websocket protocol for real-time collaboration:

### Message Types

- **Sync Messages**: Document synchronization and updates
- **Awareness Messages**: User presence and cursor information
- **Binary Protocol**: Efficient binary encoding for performance

### Connection URL Format

```
ws://localhost:3000/[documentId]?userId=[userId]
```

- `documentId`: Unique identifier for the document
- `userId`: Optional user identifier for awareness

### Automatic Features

- **Document Persistence**: Documents are kept in memory while users are connected
- **Conflict Resolution**: YJS handles all conflict resolution automatically
- **User Awareness**: Real-time cursor and selection sharing
- **Connection Recovery**: Automatic reconnection and state synchronization

## Client Integration

### JavaScript Bundle

The server provides a pre-built JavaScript bundle for easy browser integration:

**`tiptap-bundle.js`** - Complete Tiptap + YJS bundle
- Includes Tiptap editor with all collaboration features
- Exposes `TiptapEditor`, `TiptapStarterKit`, `TiptapCollaboration`, etc.
- Ready-to-use rich text collaborative editing

### Quick Start Example

```html
<!DOCTYPE html>
<html>
<head>
    <title>My Collaborative Editor</title>
    <style>
        .ProseMirror {
            border: 1px solid #ccc;
            padding: 16px;
            min-height: 200px;
            outline: none;
        }
    </style>
</head>
<body>
    <div id="editor"></div>

    <!-- Include Tiptap bundle -->
    <script src="./tiptap-bundle.js"></script>
    <script>
        // Create YJS document
        const doc = new Y.Doc();

        // Connect to collaboration server
        const provider = new WebsocketProvider('ws://localhost:3000', 'my-document', doc);

        // Create Tiptap editor
        const editor = new TiptapEditor({
            element: document.getElementById('editor'),
            extensions: [
                TiptapStarterKit.configure({ history: false }),
                TiptapCollaboration.configure({ document: doc }),
                TiptapCollaborationCursor.configure({
                    provider: provider,
                    user: { name: 'User Name', color: '#ff6b6b' }
                })
            ],
            content: '<p>Start typing...</p>',
        });
    </script>
</body>
</html>
```

### Tiptap Rich Text Editor Integration

The server now includes full support for Tiptap rich text editor with collaborative editing and cursors:

#### Browser Integration (Recommended)

```html
<!-- Include the Tiptap bundle -->
<script src="./tiptap-bundle.js"></script>

<script>
// Create YJS document
const ydoc = new Y.Doc();

// Connect to server
const provider = new WebsocketProvider('ws://localhost:3000', 'my-document', ydoc);

// Create Tiptap editor
const editor = new TiptapEditor({
  element: document.getElementById('editor'),
  extensions: [
    TiptapStarterKit.configure({
      history: false, // Important: disable history for collaboration
    }),
    TiptapCollaboration.configure({
      document: ydoc,
    }),
    TiptapCollaborationCursor.configure({
      provider: provider,
      user: {
        name: 'User Name',
        color: '#f783ac',
      },
    }),
  ],
  content: '<p>Start collaborating!</p>',
});
</script>
```

#### NPM/Module Integration

```javascript
import { Editor } from '@tiptap/core';
import StarterKit from '@tiptap/starter-kit';
import Collaboration from '@tiptap/extension-collaboration';
import CollaborationCursor from '@tiptap/extension-collaboration-cursor';
import * as Y from 'yjs';
import { WebsocketProvider } from 'y-websocket';

// Create YJS document
const ydoc = new Y.Doc();

// Connect to server
const provider = new WebsocketProvider('ws://localhost:3000', 'tiptap-document', ydoc);

// Create Tiptap editor
const editor = new Editor({
  element: document.getElementById('editor'),
  extensions: [
    StarterKit.configure({
      history: false, // Important: disable history
    }),
    Collaboration.configure({
      document: ydoc,
    }),
    CollaborationCursor.configure({
      provider: provider,
      user: {
        name: 'User Name',
        color: '#f783ac',
      },
    }),
  ],
  content: '<p>Start collaborating!</p>',
});
```

#### Tiptap Features Supported

- ✅ **Real-time collaboration** - Multiple users editing simultaneously
- ✅ **Collaborative cursors** - See other users' cursors and selections
- ✅ **Rich text formatting** - Bold, italic, headings, lists, etc.
- ✅ **Markdown shortcuts** - Type `# ` for headings, `- ` for lists
- ✅ **Undo/Redo** - Collaborative-aware history management
- ✅ **User awareness** - Real-time user presence indicators
- ✅ **Conflict resolution** - Automatic CRDT-based conflict resolution

### Live Demo

The server includes a full-featured collaborative editor demo:

**Tiptap Collaborative Editor**: `http://localhost:3000`
- Professional rich text collaborative editor
- Real-time collaborative cursors
- Multiple client support
- Connection status monitoring
- Server statistics display
- Professional UI with formatting options
- Markdown shortcuts support

### React Hook Example

```javascript
import { useEffect, useState } from 'react';
import { WebsocketProvider } from 'y-websocket';
import * as Y from 'yjs';

export function useYjsDocument(documentId, userId) {
  const [doc] = useState(() => new Y.Doc());
  const [provider, setProvider] = useState(null);
  const [connected, setConnected] = useState(false);

  useEffect(() => {
    // Create WebSocket provider
    const wsProvider = new WebsocketProvider('ws://localhost:3000', documentId, doc);

    // Handle connection status
    wsProvider.on('status', event => {
      setConnected(event.status === 'connected');
    });

    // Set user awareness
    if (userId) {
      wsProvider.awareness.setLocalStateField('user', {
        name: userId,
        color: '#' + Math.floor(Math.random()*16777215).toString(16)
      });
    }

    setProvider(wsProvider);

    return () => {
      wsProvider.destroy();
    };
  }, [documentId, userId, doc]);

  return { doc, provider, connected };
}
```

## Production Deployment

### With Nginx (Recommended)

1. **Enable nginx profile**:
   ```bash
   docker-compose --profile production up -d
   ```

2. **Configure SSL** (update `nginx.conf`):
   - Add your SSL certificates to `./ssl/`
   - Uncomment HTTPS server block
   - Update server name

### Environment Considerations

- Set `NODE_ENV=production`
- Configure proper CORS origins
- Set up SSL/TLS certificates
- Configure log rotation
- Set up monitoring and alerting
- Consider using a process manager like PM2

## Monitoring

### Health Check
```bash
curl http://localhost:3000/health
```

### Statistics
```bash
curl http://localhost:3000/api/stats
```

### Docker Health Check
```bash
docker ps  # Check container health status
```

## Development

### Running Tests
```bash
npm test
```

### Development Mode
```bash
npm run dev
```

### Docker Development
```bash
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Follow SOLID principles
4. Add tests for new functionality
5. Update documentation
6. Submit a pull request

## License

MIT License - see LICENSE file for details.

## Support

For issues and questions:
- Create an issue on GitHub
- Check the documentation
- Review the example implementations
