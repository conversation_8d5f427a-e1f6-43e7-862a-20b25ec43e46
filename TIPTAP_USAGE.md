# Tiptap Integration Guide

This guide shows you how to integrate Tiptap rich text editor with the YJS collaboration server.

## Quick Start

### 1. Basic HTML Setup

```html
<!DOCTYPE html>
<html>
<head>
    <title>My Collaborative Editor</title>
    <style>
        .ProseMirror {
            border: 1px solid #ccc;
            padding: 16px;
            min-height: 200px;
            outline: none;
        }
    </style>
</head>
<body>
    <div id="editor"></div>
    
    <!-- Include Tiptap bundle -->
    <script src="./tiptap-bundle.js"></script>
    <script>
        // Create YJS document
        const doc = new Y.Doc();
        
        // Connect to collaboration server
        const provider = new WebsocketProvider('ws://localhost:3000', 'my-document', doc);
        
        // Create Tiptap editor
        const editor = new TiptapEditor({
            element: document.getElementById('editor'),
            extensions: [
                TiptapStarterKit.configure({
                    history: false, // Disable for collaboration
                }),
                TiptapCollaboration.configure({
                    document: doc,
                }),
                TiptapCollaborationCursor.configure({
                    provider: provider,
                    user: {
                        name: 'User Name',
                        color: '#ff6b6b',
                    },
                }),
            ],
            content: '<p>Start typing...</p>',
        });
    </script>
</body>
</html>
```

### 2. With Connection Management

```html
<div id="editor"></div>
<button id="connect">Connect</button>
<button id="disconnect">Disconnect</button>

<script src="./tiptap-bundle.js"></script>
<script>
    let doc, provider, editor;
    
    function connect() {
        // Create YJS document
        doc = new Y.Doc();
        
        // Connect to server
        provider = new WebsocketProvider('ws://localhost:3000', 'my-document', doc);
        
        // Handle connection status
        provider.on('status', event => {
            console.log('Connection status:', event.status);
        });
        
        // Create editor
        editor = new TiptapEditor({
            element: document.getElementById('editor'),
            extensions: [
                TiptapStarterKit.configure({ history: false }),
                TiptapCollaboration.configure({ document: doc }),
                TiptapCollaborationCursor.configure({
                    provider: provider,
                    user: { name: 'User', color: '#4ecdc4' }
                })
            ]
        });
    }
    
    function disconnect() {
        if (provider) provider.destroy();
        if (editor) editor.destroy();
        doc = provider = editor = null;
    }
    
    document.getElementById('connect').onclick = connect;
    document.getElementById('disconnect').onclick = disconnect;
</script>
```

## Advanced Features

### Custom User Colors and Names

```javascript
// Generate random color
const colors = ['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57'];
const userColor = colors[Math.floor(Math.random() * colors.length)];

// Get user name from input or generate
const userName = document.getElementById('username').value || `User${Math.floor(Math.random() * 1000)}`;

const editor = new TiptapEditor({
    // ... other config
    extensions: [
        // ... other extensions
        TiptapCollaborationCursor.configure({
            provider: provider,
            user: {
                name: userName,
                color: userColor,
            },
        }),
    ],
});
```

### Document Persistence

```javascript
// Save document content
function saveDocument() {
    const content = editor.getHTML();
    localStorage.setItem('document-content', content);
}

// Load document content
function loadDocument() {
    const content = localStorage.getItem('document-content');
    if (content) {
        editor.commands.setContent(content);
    }
}

// Auto-save every 30 seconds
setInterval(saveDocument, 30000);
```

### Event Handling

```javascript
const editor = new TiptapEditor({
    // ... config
    onUpdate: ({ editor }) => {
        console.log('Document updated');
        // Auto-save or other logic
    },
    onCreate: ({ editor }) => {
        console.log('Editor created');
    },
    onSelectionUpdate: ({ editor }) => {
        console.log('Selection changed');
    },
});

// Listen to collaboration events
provider.on('status', event => {
    if (event.status === 'connected') {
        console.log('Connected to collaboration server');
    } else if (event.status === 'disconnected') {
        console.log('Disconnected from server');
    }
});

// Listen to awareness changes (other users)
provider.awareness.on('change', () => {
    const states = provider.awareness.getStates();
    console.log('Active users:', states.size);
});
```

## Styling

### Basic Editor Styles

```css
.ProseMirror {
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 16px;
    min-height: 200px;
    outline: none;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
}

.ProseMirror:focus {
    border-color: #007bff;
}

.ProseMirror h1, .ProseMirror h2, .ProseMirror h3 {
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    font-weight: 600;
}

.ProseMirror p {
    margin: 0 0 1em 0;
}

.ProseMirror ul, .ProseMirror ol {
    padding-left: 1.5em;
}

.ProseMirror blockquote {
    border-left: 4px solid #e9ecef;
    padding-left: 1em;
    margin: 1em 0;
    font-style: italic;
}
```

### Collaborative Cursor Styles

```css
.collaboration-cursor__caret {
    border-left: 1px solid #0d0d0d;
    border-right: 1px solid #0d0d0d;
    margin-left: -1px;
    margin-right: -1px;
    pointer-events: none;
    position: relative;
    word-break: normal;
}

.collaboration-cursor__label {
    border-radius: 3px 3px 3px 0;
    color: #0d0d0d;
    font-size: 12px;
    font-style: normal;
    font-weight: 600;
    left: -1px;
    line-height: normal;
    padding: 0.1rem 0.3rem;
    position: absolute;
    top: -1.4em;
    user-select: none;
    white-space: nowrap;
}
```

## Troubleshooting

### Common Issues

1. **Editor not loading**: Make sure `tiptap-bundle.js` is loaded before your script
2. **No collaboration**: Check WebSocket connection to `ws://localhost:3000`
3. **History conflicts**: Always disable Tiptap's history extension for collaboration
4. **Cursor not showing**: Ensure CollaborationCursor extension is properly configured

### Debug Mode

```javascript
// Enable debug logging
provider.on('status', event => console.log('Status:', event));
provider.on('connection-error', error => console.error('Error:', error));
provider.on('connection-close', event => console.log('Closed:', event));

// Check document state
console.log('Document state:', doc.getText('default').toString());
console.log('Connected users:', provider.awareness.getStates().size);
```

## Examples

See the following files for complete examples:
- `tiptap-collaborative-test.html` - Full-featured demo
- `editor-comparison.html` - Side-by-side comparison
- `working-yjs-test.html` - Simple textarea version

## Server Configuration

The YJS server is already configured for Tiptap. No server-side changes needed!

- WebSocket endpoint: `ws://localhost:3000`
- Document ID: Any string (e.g., 'my-document')
- User awareness: Automatically handled
- Conflict resolution: Built-in CRDT support
