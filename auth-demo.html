<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Django Authentication Demo</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
        }
        
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        input:focus {
            outline: none;
            border-color: #007bff;
        }
        
        button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-bottom: 10px;
        }
        
        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }
        
        .token-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            word-break: break-all;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 12px;
        }
        
        .success {
            color: #28a745;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 6px;
            margin-top: 10px;
        }
        
        .error {
            color: #dc3545;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            padding: 10px;
            border-radius: 6px;
            margin-top: 10px;
        }
        
        .instructions {
            background: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .copy-btn {
            background: #28a745;
            padding: 8px 16px;
            font-size: 14px;
            margin-top: 10px;
            width: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Django Authentication Demo</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li>Make sure your Django server is running on <code>http://localhost:8000</code></li>
                <li>Enter your Django username and password below</li>
                <li>Click "Get JWT Token" to authenticate</li>
                <li>Copy the token and use it in the collaboration editor</li>
            </ol>
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" required>
            </div>
            
            <button type="submit">Get JWT Token</button>
        </form>
        
        <div id="result"></div>
        
        <div id="tokenDisplay" class="token-display" style="display: none;">
            <strong>JWT Token:</strong>
            <div id="tokenValue"></div>
            <button class="copy-btn" onclick="copyToken()">Copy Token</button>
            <button class="copy-btn" onclick="openCollaborationEditor()">Open Collaboration Editor</button>
        </div>
    </div>

    <script>
        const DJANGO_API_URL = 'http://localhost:8000';
        
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                resultDiv.innerHTML = '<div class="success">Authenticating...</div>';
                
                const response = await fetch(`${DJANGO_API_URL}/api/auth/token/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`Authentication failed: ${response.status}`);
                }
                
                const data = await response.json();
                
                // Display success message
                resultDiv.innerHTML = '<div class="success">✅ Authentication successful!</div>';
                
                // Display token
                document.getElementById('tokenValue').textContent = data.access;
                document.getElementById('tokenDisplay').style.display = 'block';
                
                // Store token for later use
                localStorage.setItem('jwt_token', data.access);
                localStorage.setItem('refresh_token', data.refresh);
                localStorage.setItem('user_info', JSON.stringify(data.user));
                
            } catch (error) {
                console.error('Authentication error:', error);
                resultDiv.innerHTML = `<div class="error">❌ Authentication failed: ${error.message}</div>`;
                document.getElementById('tokenDisplay').style.display = 'none';
            }
        });
        
        function copyToken() {
            const tokenValue = document.getElementById('tokenValue').textContent;
            navigator.clipboard.writeText(tokenValue).then(() => {
                alert('Token copied to clipboard!');
            }).catch(err => {
                console.error('Failed to copy token:', err);
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = tokenValue;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('Token copied to clipboard!');
            });
        }
        
        function openCollaborationEditor() {
            const token = document.getElementById('tokenValue').textContent;
            const userInfo = JSON.parse(localStorage.getItem('user_info') || '{}');
            
            // Open collaboration editor with pre-filled token
            const editorUrl = `http://localhost:3000/?token=${encodeURIComponent(token)}&user=${encodeURIComponent(userInfo.username || 'User')}`;
            window.open(editorUrl, '_blank');
        }
        
        // Check if we have a stored token
        window.addEventListener('load', () => {
            const storedToken = localStorage.getItem('jwt_token');
            if (storedToken) {
                document.getElementById('tokenValue').textContent = storedToken;
                document.getElementById('tokenDisplay').style.display = 'block';
                document.getElementById('result').innerHTML = '<div class="success">✅ Using stored token</div>';
            }
        });
        
        // Auto-fill demo credentials (remove in production)
        document.getElementById('username').value = 'admin';
        document.getElementById('password').value = 'admin';
    </script>
</body>
</html>
