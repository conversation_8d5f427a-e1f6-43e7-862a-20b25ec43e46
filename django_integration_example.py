# Django Integration Example
# This file shows how to implement the Django side of the authentication system

"""
Django Views and Models for WebSocket Collaboration Authentication

Add these to your Django project to enable secure authentication
with the WebSocket collaboration server.

Requirements:
- pip install djangorestframework
- pip install djangorestframework-simplejwt
- pip install redis
"""

from django.contrib.auth.models import User
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from rest_framework_simplejwt.views import TokenObtainPairView
import json
import redis
import logging

logger = logging.getLogger(__name__)

# Redis connection for caching
redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)

class CollaborationTokenObtainPairView(TokenObtainPairView):
    """
    Custom JWT token view that includes user permissions
    """
    def post(self, request, *args, **kwargs):
        response = super().post(request, *args, **kwargs)
        
        if response.status_code == 200:
            user = User.objects.get(username=request.data['username'])
            
            # Add user information to token response
            response.data.update({
                'user': {
                    'id': user.id,
                    'username': user.username,
                    'email': user.email,
                    'first_name': user.first_name,
                    'last_name': user.last_name,
                    'is_staff': user.is_staff,
                    'permissions': list(user.user_permissions.values_list('codename', flat=True)),
                    'groups': list(user.groups.values_list('name', flat=True))
                }
            })
            
        return response

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def verify_user(request):
    """
    Verify user for WebSocket authentication
    """
    try:
        user_id = request.GET.get('user_id')
        
        if not user_id:
            return Response({'error': 'user_id parameter required'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        # Verify the user exists and is active
        try:
            user = User.objects.get(id=user_id, is_active=True)
        except User.DoesNotExist:
            return Response({'error': 'User not found or inactive'}, 
                          status=status.HTTP_404_NOT_FOUND)
        
        # Return user information
        user_data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'is_active': user.is_active,
            'is_staff': user.is_staff,
            'last_login': user.last_login.isoformat() if user.last_login else None,
            'permissions': list(user.user_permissions.values_list('codename', flat=True)),
            'groups': list(user.groups.values_list('name', flat=True))
        }
        
        return Response(user_data)
        
    except Exception as e:
        logger.error(f"User verification failed: {str(e)}")
        return Response({'error': 'Internal server error'}, 
                       status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_document_access(request, document_id):
    """
    Check if user has access to a specific document
    """
    try:
        user_id = request.GET.get('user_id')
        
        if not user_id:
            return Response({'error': 'user_id parameter required'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        # Get user
        try:
            user = User.objects.get(id=user_id, is_active=True)
        except User.DoesNotExist:
            return Response({'error': 'User not found'}, 
                          status=status.HTTP_404_NOT_FOUND)
        
        # Check document access (implement your own logic here)
        has_access = check_user_document_permission(user, document_id)
        
        # Determine user role and permissions
        permissions = get_user_document_permissions(user, document_id)
        role = get_user_document_role(user, document_id)
        
        return Response({
            'has_access': has_access,
            'permissions': permissions,
            'role': role,
            'document_id': document_id,
            'user_id': user.id
        })
        
    except Exception as e:
        logger.error(f"Document access check failed: {str(e)}")
        return Response({'error': 'Internal server error'}, 
                       status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_permissions(request):
    """
    Get user permissions for collaboration
    """
    try:
        user_id = request.GET.get('user_id')
        document_id = request.GET.get('document_id')
        
        if not user_id:
            return Response({'error': 'user_id parameter required'}, 
                          status=status.HTTP_400_BAD_REQUEST)
        
        user = User.objects.get(id=user_id, is_active=True)
        
        # Get document-specific permissions
        if document_id:
            permissions = get_user_document_permissions(user, document_id)
        else:
            # Get general permissions
            permissions = list(user.user_permissions.values_list('codename', flat=True))
        
        return Response({
            'permissions': permissions,
            'user_id': user.id,
            'document_id': document_id
        })
        
    except User.DoesNotExist:
        return Response({'error': 'User not found'}, 
                       status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"Get user permissions failed: {str(e)}")
        return Response({'error': 'Internal server error'}, 
                       status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def user_activity(request):
    """
    Log user activity for collaboration
    """
    try:
        data = request.data
        user_id = data.get('user_id')
        document_id = data.get('document_id')
        action = data.get('action')
        timestamp = data.get('timestamp')
        
        # Log the activity (implement your own logging logic)
        log_user_activity(user_id, document_id, action, timestamp)
        
        return Response({'status': 'logged'})
        
    except Exception as e:
        logger.error(f"User activity logging failed: {str(e)}")
        return Response({'error': 'Internal server error'}, 
                       status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_document(request, document_id):
    """
    Get document metadata
    """
    try:
        # Implement your document model logic here
        document = get_document_by_id(document_id)
        
        if not document:
            return Response({'error': 'Document not found'}, 
                          status=status.HTTP_404_NOT_FOUND)
        
        # Check if user has access
        if not check_user_document_permission(request.user, document_id):
            return Response({'error': 'Access denied'}, 
                          status=status.HTTP_403_FORBIDDEN)
        
        return Response({
            'id': document_id,
            'title': document.get('title', 'Untitled'),
            'created_at': document.get('created_at'),
            'updated_at': document.get('updated_at'),
            'owner': document.get('owner'),
            'collaborators': document.get('collaborators', [])
        })
        
    except Exception as e:
        logger.error(f"Get document failed: {str(e)}")
        return Response({'error': 'Internal server error'}, 
                       status=status.HTTP_500_INTERNAL_SERVER_ERROR)

# Helper functions (implement according to your business logic)

def check_user_document_permission(user, document_id):
    """
    Check if user has permission to access document
    Implement your own logic here
    """
    # Example logic:
    # - Check if user is owner
    # - Check if user is in collaborators list
    # - Check if document is public
    # - Check user groups/permissions
    
    # For demo purposes, allow all authenticated users
    return user.is_authenticated

def get_user_document_permissions(user, document_id):
    """
    Get user's permissions for a specific document
    """
    # Example permissions based on user role
    if user.is_staff:
        return ['read', 'write', 'delete', 'share']
    elif check_user_document_permission(user, document_id):
        return ['read', 'write']
    else:
        return []

def get_user_document_role(user, document_id):
    """
    Get user's role for a specific document
    """
    if user.is_staff:
        return 'admin'
    elif check_user_document_permission(user, document_id):
        return 'editor'
    else:
        return 'viewer'

def log_user_activity(user_id, document_id, action, timestamp):
    """
    Log user activity for audit purposes
    """
    # Implement your logging logic here
    # Could store in database, send to analytics, etc.
    logger.info(f"User {user_id} performed {action} on document {document_id} at {timestamp}")

def get_document_by_id(document_id):
    """
    Get document by ID
    Implement your document model logic here
    """
    # Example implementation
    # return Document.objects.get(id=document_id)
    
    # For demo purposes, return mock data
    return {
        'id': document_id,
        'title': f'Document {document_id}',
        'created_at': '2024-01-01T00:00:00Z',
        'updated_at': '2024-01-01T00:00:00Z',
        'owner': 1,
        'collaborators': []
    }

# URL patterns (add to your urls.py)
"""
from django.urls import path
from . import views

urlpatterns = [
    path('api/auth/token/', views.CollaborationTokenObtainPairView.as_view(), name='token_obtain_pair'),
    path('api/auth/verify-user/', views.verify_user, name='verify_user'),
    path('api/auth/user-permissions/', views.get_user_permissions, name='user_permissions'),
    path('api/documents/<str:document_id>/check-access/', views.check_document_access, name='check_document_access'),
    path('api/documents/<str:document_id>/', views.get_document, name='get_document'),
    path('api/collaboration/user-activity/', views.user_activity, name='user_activity'),
]
"""

# Settings.py additions
"""
INSTALLED_APPS = [
    # ... your other apps
    'rest_framework',
    'rest_framework_simplejwt',
]

REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': (
        'rest_framework_simplejwt.authentication.JWTAuthentication',
    ),
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
}

from datetime import timedelta

SIMPLE_JWT = {
    'ACCESS_TOKEN_LIFETIME': timedelta(hours=24),
    'REFRESH_TOKEN_LIFETIME': timedelta(days=7),
    'ROTATE_REFRESH_TOKENS': True,
    'BLACKLIST_AFTER_ROTATION': True,
    'ALGORITHM': 'HS256',
    'SIGNING_KEY': SECRET_KEY,  # Use your Django SECRET_KEY
}

# Redis settings
REDIS_URL = 'redis://localhost:6379/0'

# CORS settings (if using django-cors-headers)
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",  # Your WebSocket server
]
"""
