# Docker Compose for Django + WebSocket Collaboration Server with Authentication
# This configuration includes Django, WebSocket server, Redis, and PostgreSQL

version: '3.8'

services:
  # PostgreSQL Database for Django
  postgres:
    image: postgres:15-alpine
    container_name: collaboration_postgres
    environment:
      POSTGRES_DB: collaboration_db
      POSTGRES_USER: collaboration_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password_change_me}
      POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - collaboration_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U collaboration_user -d collaboration_db"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    container_name: collaboration_redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password_change_me}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - collaboration_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Django Application
  django:
    build:
      context: ./django_app
      dockerfile: Dockerfile
    container_name: collaboration_django
    environment:
      - DEBUG=${DEBUG:-False}
      - SECRET_KEY=${DJANGO_SECRET_KEY:-your-super-secret-django-key-change-in-production}
      - DATABASE_URL=postgresql://collaboration_user:${POSTGRES_PASSWORD:-secure_password_change_me}@postgres:5432/collaboration_db
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password_change_me}@redis:6379/0
      - ALLOWED_HOSTS=localhost,127.0.0.1,django,websocket
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://websocket:3000
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-super-secret-jwt-key-change-in-production}
      - WEBSOCKET_SERVER_URL=http://websocket:3000
    volumes:
      - ./django_app:/app
      - django_media:/app/media
      - django_static:/app/static
    ports:
      - "8000:8000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - collaboration_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # WebSocket Collaboration Server
  websocket:
    build:
      context: .
      dockerfile: Dockerfile.auth
    container_name: collaboration_websocket
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - PORT=3000
      - HOST=0.0.0.0
      - JWT_SECRET=${JWT_SECRET_KEY:-your-super-secret-jwt-key-change-in-production}
      - DJANGO_API_URL=http://django:8000
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password_change_me}@redis:6379/0
      - CORS_ORIGINS=http://localhost:8000,http://django:8000
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - ENABLE_AUTH_LOGS=true
      - MAX_CONNECTIONS_PER_USER=5
      - RATE_LIMIT_WINDOW=60000
      - RATE_LIMIT_MAX=100
    volumes:
      - ./logs:/app/logs
      - websocket_data:/app/data
    ports:
      - "3000:3000"
    depends_on:
      django:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - collaboration_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    container_name: collaboration_nginx
    volumes:
      - ./nginx.auth.conf:/etc/nginx/nginx.conf:ro
      - django_static:/var/www/static:ro
      - django_media:/var/www/media:ro
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - django
      - websocket
    networks:
      - collaboration_network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  django_media:
    driver: local
  django_static:
    driver: local
  websocket_data:
    driver: local

networks:
  collaboration_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
