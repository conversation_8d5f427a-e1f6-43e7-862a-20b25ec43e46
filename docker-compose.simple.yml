# Simplified <PERSON><PERSON> Compose for WebSocket Collaboration Server with Authentication
# This version focuses on the WebSocket server + Redis, without Django container

version: '3.8'

services:
  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    container_name: collaboration_redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password_change_me}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - collaboration_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # WebSocket Collaboration Server with Authentication
  websocket:
    build:
      context: .
      dockerfile: Dockerfile.auth
    container_name: collaboration_websocket
    env_file:
      - .env
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=3000
      - HOST=0.0.0.0
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password_change_me}@redis:6379/0
      - CORS_ORIGINS=http://localhost:8000,http://host.docker.internal:8000
    volumes:
      - ./logs:/app/logs
      - websocket_data:/app/data
    ports:
      - "3000:3000"
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - collaboration_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:
    driver: local
  websocket_data:
    driver: local

networks:
  collaboration_network:
    driver: bridge
