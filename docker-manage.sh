#!/bin/bash

# Docker Management Script for Realtime YJS Server
# This script provides easy management of the Docker container

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
CONTAINER_NAME="realtime-yjs-server"
IMAGE_NAME="realtime-yjs-server"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start          Start the container"
    echo "  stop           Stop the container"
    echo "  restart        Restart the container"
    echo "  status         Show container status"
    echo "  logs           Show container logs"
    echo "  logs-follow    Follow container logs"
    echo "  shell          Open shell in container"
    echo "  clean          Stop and remove container"
    echo "  rebuild        Rebuild the Docker image"
    echo "  health         Check application health"
    echo "  help           Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start       # Start the container"
    echo "  $0 logs        # View logs"
    echo "  $0 health      # Check health"
}

# Function to check if container exists
container_exists() {
    docker ps -a --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"
}

# Function to check if container is running
container_running() {
    docker ps --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"
}

# Function to start container
start_container() {
    if container_running; then
        print_warning "Container is already running"
        return 0
    fi
    
    if container_exists; then
        print_status "Starting existing container..."
        docker start "$CONTAINER_NAME"
        print_success "Container started"
    else
        print_status "Container doesn't exist. Use docker-run.sh to create and start it."
        return 1
    fi
}

# Function to stop container
stop_container() {
    if ! container_running; then
        print_warning "Container is not running"
        return 0
    fi
    
    print_status "Stopping container..."
    docker stop "$CONTAINER_NAME"
    print_success "Container stopped"
}

# Function to restart container
restart_container() {
    if container_exists; then
        print_status "Restarting container..."
        docker restart "$CONTAINER_NAME"
        print_success "Container restarted"
    else
        print_error "Container doesn't exist"
        return 1
    fi
}

# Function to show status
show_status() {
    if container_exists; then
        print_status "Container status:"
        docker ps -a --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        
        if container_running; then
            echo ""
            print_status "Container stats:"
            docker stats "$CONTAINER_NAME" --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
        fi
    else
        print_warning "Container doesn't exist"
    fi
}

# Function to show logs
show_logs() {
    if container_exists; then
        docker logs "$CONTAINER_NAME"
    else
        print_error "Container doesn't exist"
        return 1
    fi
}

# Function to follow logs
follow_logs() {
    if container_exists; then
        print_status "Following logs (Press Ctrl+C to stop)..."
        docker logs -f "$CONTAINER_NAME"
    else
        print_error "Container doesn't exist"
        return 1
    fi
}

# Function to open shell
open_shell() {
    if container_running; then
        print_status "Opening shell in container..."
        docker exec -it "$CONTAINER_NAME" /bin/sh
    else
        print_error "Container is not running"
        return 1
    fi
}

# Function to clean container
clean_container() {
    if container_exists; then
        print_status "Stopping and removing container..."
        docker stop "$CONTAINER_NAME" > /dev/null 2>&1 || true
        docker rm "$CONTAINER_NAME"
        print_success "Container removed"
    else
        print_warning "Container doesn't exist"
    fi
}

# Function to rebuild image
rebuild_image() {
    print_status "Rebuilding Docker image..."
    docker build -t "$IMAGE_NAME" .
    print_success "Image rebuilt successfully"
}

# Function to check health
check_health() {
    if container_running; then
        print_status "Checking application health..."
        
        # Get container port mapping
        PORT=$(docker port "$CONTAINER_NAME" 3000 | cut -d: -f2)
        
        if [[ -n "$PORT" ]]; then
            if curl -s "http://localhost:$PORT/health" > /dev/null; then
                print_success "Application is healthy"
                curl -s "http://localhost:$PORT/health" | jq . 2>/dev/null || curl -s "http://localhost:$PORT/health"
            else
                print_error "Application health check failed"
                return 1
            fi
        else
            print_error "Could not determine container port"
            return 1
        fi
    else
        print_error "Container is not running"
        return 1
    fi
}

# Main script logic
case "${1:-help}" in
    start)
        start_container
        ;;
    stop)
        stop_container
        ;;
    restart)
        restart_container
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    logs-follow)
        follow_logs
        ;;
    shell)
        open_shell
        ;;
    clean)
        clean_container
        ;;
    rebuild)
        rebuild_image
        ;;
    health)
        check_health
        ;;
    help|--help|-h)
        show_usage
        ;;
    *)
        print_error "Unknown command: $1"
        show_usage
        exit 1
        ;;
esac
