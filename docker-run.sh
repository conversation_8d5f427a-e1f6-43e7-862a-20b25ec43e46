#!/bin/bash

# Docker Run Script for Realtime YJS Server
# This script provides an easy way to run the Docker container with proper configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
CONTAINER_NAME="realtime-yjs-server"
IMAGE_NAME="realtime-yjs-server"
PORT="3000"
ENV_FILE=".env.docker"
DETACHED=true
REBUILD=false

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -p, --port PORT        Port to expose (default: 3000)"
    echo "  -n, --name NAME        Container name (default: realtime-yjs-server)"
    echo "  -e, --env-file FILE    Environment file (default: .env.docker)"
    echo "  -f, --foreground       Run in foreground (default: detached)"
    echo "  -r, --rebuild          Rebuild the Docker image before running"
    echo "  -h, --help             Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0                     # Run with default settings"
    echo "  $0 -p 8080             # Run on port 8080"
    echo "  $0 -f                  # Run in foreground"
    echo "  $0 -r                  # Rebuild and run"
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -n|--name)
            CONTAINER_NAME="$2"
            shift 2
            ;;
        -e|--env-file)
            ENV_FILE="$2"
            shift 2
            ;;
        -f|--foreground)
            DETACHED=false
            shift
            ;;
        -r|--rebuild)
            REBUILD=true
            shift
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Check if environment file exists
if [[ ! -f "$ENV_FILE" ]]; then
    print_warning "Environment file '$ENV_FILE' not found. Using default environment variables."
    ENV_FILE=""
fi

print_status "Starting Realtime YJS Server Docker container..."
print_status "Container name: $CONTAINER_NAME"
print_status "Port: $PORT"
print_status "Environment file: ${ENV_FILE:-"None (using defaults)"}"

# Stop and remove existing container if it exists
if docker ps -a --format 'table {{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
    print_status "Stopping and removing existing container..."
    docker stop "$CONTAINER_NAME" > /dev/null 2>&1 || true
    docker rm "$CONTAINER_NAME" > /dev/null 2>&1 || true
fi

# Rebuild image if requested
if [[ "$REBUILD" == true ]]; then
    print_status "Rebuilding Docker image..."
    docker build -t "$IMAGE_NAME" .
    print_success "Docker image rebuilt successfully"
fi

# Check if image exists
if ! docker image inspect "$IMAGE_NAME" > /dev/null 2>&1; then
    print_status "Docker image not found. Building..."
    docker build -t "$IMAGE_NAME" .
    print_success "Docker image built successfully"
fi

# Prepare docker run command
DOCKER_CMD="docker run"

# Add detached flag if needed
if [[ "$DETACHED" == true ]]; then
    DOCKER_CMD="$DOCKER_CMD -d"
fi

# Add container name and port mapping
DOCKER_CMD="$DOCKER_CMD --name $CONTAINER_NAME -p $PORT:3000"

# Add environment file if it exists
if [[ -n "$ENV_FILE" ]]; then
    DOCKER_CMD="$DOCKER_CMD --env-file $ENV_FILE"
fi

# Add volume for logs (optional)
DOCKER_CMD="$DOCKER_CMD -v $(pwd)/logs:/app/logs"

# Add restart policy
DOCKER_CMD="$DOCKER_CMD --restart unless-stopped"

# Add image name
DOCKER_CMD="$DOCKER_CMD $IMAGE_NAME"

# Run the container
print_status "Running: $DOCKER_CMD"
eval $DOCKER_CMD

if [[ "$DETACHED" == true ]]; then
    print_success "Container started successfully!"
    print_status "Container ID: $(docker ps -q -f name=$CONTAINER_NAME)"
    print_status "Access the application at: http://localhost:$PORT"
    print_status ""
    print_status "Useful commands:"
    print_status "  View logs:    docker logs $CONTAINER_NAME"
    print_status "  Follow logs:  docker logs -f $CONTAINER_NAME"
    print_status "  Stop:         docker stop $CONTAINER_NAME"
    print_status "  Restart:      docker restart $CONTAINER_NAME"
    print_status "  Remove:       docker rm $CONTAINER_NAME"
else
    print_status "Container running in foreground. Press Ctrl+C to stop."
fi
