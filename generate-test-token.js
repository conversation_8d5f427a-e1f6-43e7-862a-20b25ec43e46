#!/usr/bin/env node

/**
 * Simple JWT Token Generator for Testing
 * This script generates valid JWT tokens for testing the WebSocket authentication
 */

const jwt = require('jsonwebtoken');

// Configuration (should match your server settings)
const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';

// Default test user data
const defaultUser = {
    user_id: 1,
    username: 'testuser',
    email: '<EMAIL>',
    permissions: ['read', 'write'],
    exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60), // 24 hours from now
    iat: Math.floor(Date.now() / 1000) // issued at now
};

function generateToken(userData = defaultUser) {
    try {
        const token = jwt.sign(userData, JWT_SECRET, { algorithm: 'HS256' });
        return token;
    } catch (error) {
        console.error('Error generating token:', error);
        return null;
    }
}

function verifyToken(token) {
    try {
        const decoded = jwt.verify(token, JWT_SECRET);
        return decoded;
    } catch (error) {
        console.error('Error verifying token:', error);
        return null;
    }
}

// Command line interface
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        // Generate default token
        console.log('🔧 Generating test JWT token...\n');
        
        const token = generateToken();
        if (token) {
            console.log('✅ Token generated successfully!\n');
            console.log('User Data:');
            console.log(JSON.stringify(defaultUser, null, 2));
            console.log('\nJWT Token:');
            console.log(token);
            console.log('\n📋 Copy this token and use it in the collaboration editor');
            console.log(`🌐 Or open: http://localhost:3000/?token=${encodeURIComponent(token)}&user=${encodeURIComponent(defaultUser.username)}`);
        }
    } else if (args[0] === 'verify' && args[1]) {
        // Verify provided token
        console.log('🔍 Verifying token...\n');
        
        const decoded = verifyToken(args[1]);
        if (decoded) {
            console.log('✅ Token is valid!\n');
            console.log('Decoded payload:');
            console.log(JSON.stringify(decoded, null, 2));
        } else {
            console.log('❌ Token is invalid or expired');
        }
    } else if (args[0] === 'custom') {
        // Generate custom token
        console.log('🔧 Generating custom JWT token...\n');
        
        const customUser = {
            user_id: parseInt(args[1]) || 1,
            username: args[2] || 'testuser',
            email: args[3] || '<EMAIL>',
            permissions: ['read', 'write'],
            exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60),
            iat: Math.floor(Date.now() / 1000)
        };
        
        const token = generateToken(customUser);
        if (token) {
            console.log('✅ Custom token generated successfully!\n');
            console.log('User Data:');
            console.log(JSON.stringify(customUser, null, 2));
            console.log('\nJWT Token:');
            console.log(token);
            console.log('\n📋 Copy this token and use it in the collaboration editor');
            console.log(`🌐 Or open: http://localhost:3000/?token=${encodeURIComponent(token)}&user=${encodeURIComponent(customUser.username)}`);
        }
    } else {
        // Show usage
        console.log('JWT Token Generator for Testing\n');
        console.log('Usage:');
        console.log('  node generate-test-token.js                    # Generate default test token');
        console.log('  node generate-test-token.js verify <token>     # Verify a token');
        console.log('  node generate-test-token.js custom <id> <username> <email>  # Generate custom token');
        console.log('\nExamples:');
        console.log('  node generate-test-token.js');
        console.log('  node generate-test-token.js custom 2 alice <EMAIL>');
        console.log('  node generate-test-token.js verify eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...');
    }
}

module.exports = {
    generateToken,
    verifyToken,
    JWT_SECRET
};
