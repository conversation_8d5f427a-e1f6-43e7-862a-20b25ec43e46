#!/bin/bash

# Restart Server Script for Tiptap Collaborative Editor
# This script cleanly stops any running processes and starts the server fresh

echo "🧹 Cleaning up existing processes..."

# Kill any existing Node.js processes for this project
pkill -f "node.*src/index.js" 2>/dev/null || true
pkill -f "nodemon" 2>/dev/null || true

# Stop and remove any Docker containers
echo "🐳 Cleaning up Docker containers..."
docker stop $(docker ps -aq) 2>/dev/null || true
docker rm $(docker ps -aq) 2>/dev/null || true

# Clear logs
echo "📝 Clearing old logs..."
echo "" > logs/combined.log 2>/dev/null || true
echo "" > logs/error.log 2>/dev/null || true

# Wait a moment for cleanup
sleep 2

echo "🚀 Starting fresh server..."
npm start
