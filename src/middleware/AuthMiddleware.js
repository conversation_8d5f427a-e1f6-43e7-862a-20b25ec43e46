/**
 * Authentication Middleware for WebSocket Server
 * Handles JWT token validation and user authentication from Django
 */

const jwt = require('jsonwebtoken');
const axios = require('axios');
const Redis = require('ioredis');
const Logger = require('../utils/Logger');

class AuthMiddleware {
    constructor(config = {}) {
        this.jwtSecret = config.jwtSecret || process.env.JWT_SECRET || 'your-secret-key';
        this.djangoApiUrl = config.djangoApiUrl || process.env.DJANGO_API_URL || 'http://localhost:8000';
        this.redisUrl = config.redisUrl || process.env.REDIS_URL || 'redis://localhost:6379';
        this.testMode = config.testMode || process.env.AUTH_TEST_MODE === 'true';
        
        // Initialize Redis client for caching user sessions
        this.redis = new Redis(this.redisUrl, {
            retryDelayOnFailover: 100,
            maxRetriesPerRequest: 3,
            lazyConnect: true
        });
        
        // Initialize logger instance
        this.logger = new Logger();

        this.redis.on('connect', () => {
            this.logger.info('Redis connected for authentication cache');
        });

        this.redis.on('error', (err) => {
            this.logger.error('Redis connection error:', err);
        });
    }

    /**
     * Validate JWT token and extract user information
     * @param {string} token - JWT token from client
     * @returns {Object} - User information or null if invalid
     */
    async validateToken(token) {
        try {
            if (!token) {
                throw new Error('No token provided');
            }

            // Remove 'Bearer ' prefix if present
            const cleanToken = token.replace(/^Bearer\s+/, '');

            // In test mode, bypass JWT verification and create a mock user
            if (this.testMode) {
                this.logger.info('Test mode enabled - bypassing JWT verification', {
                    service: 'realtime-yjs-server',
                    tokenLength: cleanToken.length
                });

                // Try to decode the token without verification to extract user info
                let userInfo = {};
                try {
                    const decoded = jwt.decode(cleanToken);
                    if (decoded) {
                        userInfo = {
                            userId: decoded.user_id || decoded.userId || decoded.sub || 1,
                            username: decoded.username || 'testuser',
                            email: decoded.email || '<EMAIL>',
                            permissions: decoded.permissions || ['read', 'write'],
                            exp: decoded.exp,
                            iat: decoded.iat
                        };
                    }
                } catch (decodeError) {
                    this.logger.info('Could not decode token, using default test user', {
                        service: 'realtime-yjs-server'
                    });
                }

                // Return default test user if decoding failed
                return {
                    userId: userInfo.userId || 1,
                    username: userInfo.username || 'testuser',
                    email: userInfo.email || '<EMAIL>',
                    permissions: userInfo.permissions || ['read', 'write'],
                    exp: userInfo.exp || Math.floor(Date.now() / 1000) + 86400, // 24 hours from now
                    iat: userInfo.iat || Math.floor(Date.now() / 1000)
                };
            }

            this.logger.info(`Attempting JWT verification with secret: ${this.jwtSecret ? 'SECRET_SET' : 'SECRET_NOT_SET'}`, {
                service: 'realtime-yjs-server',
                tokenLength: cleanToken.length
            });

            // Verify JWT signature
            const decoded = jwt.verify(cleanToken, this.jwtSecret);

            this.logger.info('JWT verification successful', {
                service: 'realtime-yjs-server',
                userId: decoded.user_id || decoded.sub,
                username: decoded.username,
                exp: decoded.exp,
                currentTime: Math.floor(Date.now() / 1000)
            });

            // Check if token is expired
            if (decoded.exp && Date.now() >= decoded.exp * 1000) {
                throw new Error('Token expired');
            }

            return {
                userId: decoded.user_id || decoded.sub,
                username: decoded.username,
                email: decoded.email,
                permissions: decoded.permissions || [],
                exp: decoded.exp,
                iat: decoded.iat
            };
        } catch (error) {
            this.logger.warn('Token validation failed:', {
                service: 'realtime-yjs-server',
                error: error.message,
                tokenLength: token ? token.length : 0,
                secretSet: this.jwtSecret ? 'YES' : 'NO',
                testMode: this.testMode
            });
            return null;
        }
    }

    /**
     * Verify user with Django backend
     * @param {Object} userInfo - User information from JWT
     * @returns {Object} - Verified user data with permissions
     */
    async verifyUserWithDjango(userInfo) {
        try {
            // Test mode: bypass Django verification
            if (this.testMode) {
                this.logger.info('Test mode: bypassing Django verification', {
                    userId: userInfo.user_id || userInfo.userId,
                    username: userInfo.username
                });

                return {
                    id: userInfo.user_id || userInfo.userId || 1,
                    username: userInfo.username || 'testuser',
                    email: userInfo.email || '<EMAIL>',
                    isActive: true,
                    permissions: userInfo.permissions || ['read', 'write'],
                    groups: [],
                    lastLogin: new Date().toISOString()
                };
            }

            const cacheKey = `user:${userInfo.userId}`;

            // Check cache first
            const cachedUser = await this.redis.get(cacheKey);
            if (cachedUser) {
                this.logger.info('User found in cache', { userId: userInfo.userId });
                return JSON.parse(cachedUser);
            }

            // Verify with Django API
            const response = await axios.get(`${this.djangoApiUrl}/api/auth/verify-user/`, {
                headers: {
                    'Authorization': `Bearer ${userInfo.token}`,
                    'Content-Type': 'application/json'
                },
                params: {
                    user_id: userInfo.userId
                },
                timeout: 5000
            });

            const verifiedUser = {
                id: response.data.id,
                username: response.data.username,
                email: response.data.email,
                firstName: response.data.first_name,
                lastName: response.data.last_name,
                permissions: response.data.permissions || [],
                groups: response.data.groups || [],
                isActive: response.data.is_active,
                isStaff: response.data.is_staff,
                lastLogin: response.data.last_login
            };

            // Cache user data for 15 minutes
            await this.redis.setex(cacheKey, 900, JSON.stringify(verifiedUser));
            
            this.logger.info('User verified with Django', { userId: userInfo.userId });
            return verifiedUser;

        } catch (error) {
            this.logger.error('Django user verification failed:', {
                userId: userInfo.userId,
                error: error.message
            });
            return null;
        }
    }

    /**
     * Check if user has permission to access a document
     * @param {Object} user - User object
     * @param {string} documentId - Document ID
     * @returns {boolean} - Whether user can access document
     */
    async checkDocumentAccess(user, documentId) {
        try {
            // In test mode, bypass document access checks
            if (this.testMode) {
                this.logger.info('Test mode: bypassing document access check', {
                    userId: user.id,
                    documentId,
                    service: 'realtime-yjs-server'
                });
                return true; // Allow all access in test mode
            }

            const cacheKey = `access:${user.id}:${documentId}`;

            // Check cache first
            const cachedAccess = await this.redis.get(cacheKey);
            if (cachedAccess !== null) {
                return cachedAccess === 'true';
            }

            // Check with Django API
            const response = await axios.get(`${this.djangoApiUrl}/api/documents/${documentId}/check-access/`, {
                headers: {
                    'Authorization': `Bearer ${user.token}`,
                    'Content-Type': 'application/json'
                },
                params: {
                    user_id: user.id
                },
                timeout: 5000
            });

            const hasAccess = response.data.has_access === true;

            // Cache access decision for 5 minutes
            await this.redis.setex(cacheKey, 300, hasAccess.toString());

            return hasAccess;

        } catch (error) {
            this.logger.error('Document access check failed:', {
                userId: user.id,
                documentId,
                error: error.message
            });
            // Default to deny access on error
            return false;
        }
    }

    /**
     * Authenticate WebSocket connection
     * @param {Object} socket - WebSocket connection
     * @param {Function} next - Next middleware function
     */
    async authenticateConnection(socket, next) {
        try {
            const token = socket.handshake.auth?.token || 
                         socket.handshake.headers?.authorization ||
                         socket.handshake.query?.token;

            if (!token) {
                return next(new Error('Authentication token required'));
            }

            // Validate JWT token
            const userInfo = await this.validateToken(token);
            if (!userInfo) {
                return next(new Error('Invalid or expired token'));
            }

            // Verify user with Django
            userInfo.token = token.replace(/^Bearer\s+/, '');
            const verifiedUser = await this.verifyUserWithDjango(userInfo);
            if (!verifiedUser || !verifiedUser.isActive) {
                return next(new Error('User not found or inactive'));
            }

            // Attach user info to socket
            socket.user = verifiedUser;
            socket.user.token = userInfo.token;
            
            this.logger.info('WebSocket connection authenticated', {
                userId: verifiedUser.id,
                username: verifiedUser.username,
                socketId: socket.id
            });

            next();

        } catch (error) {
            this.logger.error('WebSocket authentication failed:', error.message);
            next(new Error('Authentication failed'));
        }
    }

    /**
     * Middleware for document access authorization
     * @param {Object} socket - WebSocket connection
     * @param {string} documentId - Document ID to access
     * @returns {boolean} - Whether access is granted
     */
    async authorizeDocumentAccess(socket, documentId) {
        try {
            if (!socket.user) {
                this.logger.warn('Unauthorized document access attempt', { documentId });
                return false;
            }

            const hasAccess = await this.checkDocumentAccess(socket.user, documentId);
            
            if (hasAccess) {
                this.logger.info('Document access granted', {
                    userId: socket.user.id,
                    documentId,
                    socketId: socket.id
                });
            } else {
                this.logger.warn('Document access denied', {
                    userId: socket.user.id,
                    documentId,
                    socketId: socket.id
                });
            }

            return hasAccess;

        } catch (error) {
            this.logger.error('Document authorization error:', error.message);
            return false;
        }
    }

    /**
     * Clean up user session on disconnect
     * @param {Object} socket - WebSocket connection
     */
    async cleanupUserSession(socket) {
        if (socket.user) {
            this.logger.info('Cleaning up user session', {
                userId: socket.user.id,
                socketId: socket.id
            });
            
            // Could implement session cleanup logic here
            // For example, updating last seen timestamp
        }
    }

    /**
     * Get user session info
     * @param {string} userId - User ID
     * @returns {Object} - User session data
     */
    async getUserSession(userId) {
        try {
            const cacheKey = `user:${userId}`;
            const cachedUser = await this.redis.get(cacheKey);
            return cachedUser ? JSON.parse(cachedUser) : null;
        } catch (error) {
            this.logger.error('Failed to get user session:', error.message);
            return null;
        }
    }

    /**
     * Close Redis connection
     */
    async close() {
        if (this.redis) {
            await this.redis.quit();
        }
    }
}

module.exports = AuthMiddleware;
