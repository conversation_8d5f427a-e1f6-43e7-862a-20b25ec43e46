/**
 * Django Integration Service
 * Handles communication between WebSocket server and Django backend
 */

const axios = require('axios');
const Redis = require('ioredis');
const Logger = require('../utils/Logger');
const AuthConfig = require('../config/AuthConfig');

class DjangoIntegrationService {
    constructor(config = {}) {
        this.djangoApiUrl = config.djangoApiUrl || AuthConfig.django.apiUrl;
        this.apiTimeout = config.apiTimeout || AuthConfig.django.apiTimeout;
        this.redisUrl = config.redisUrl || AuthConfig.redis.url;
        
        // Initialize Redis for caching
        this.redis = new Redis(this.redisUrl, {
            retryDelayOnFailover: 100,
            maxRetriesPerRequest: 3,
            lazyConnect: true,
            keyPrefix: AuthConfig.redis.keyPrefix
        });
        
        this.logger = Logger;
        
        // Setup axios instance with default config
        this.apiClient = axios.create({
            baseURL: this.djangoApiUrl,
            timeout: this.apiTimeout,
            headers: {
                'Content-Type': 'application/json',
                'User-Agent': 'WebSocket-Collaboration-Server/1.0'
            }
        });
        
        // Add request interceptor for logging
        this.apiClient.interceptors.request.use(
            (config) => {
                this.logger.debug('Django API request', {
                    method: config.method,
                    url: config.url,
                    params: config.params
                });
                return config;
            },
            (error) => {
                this.logger.error('Django API request error', error);
                return Promise.reject(error);
            }
        );
        
        // Add response interceptor for logging
        this.apiClient.interceptors.response.use(
            (response) => {
                this.logger.debug('Django API response', {
                    status: response.status,
                    url: response.config.url
                });
                return response;
            },
            (error) => {
                this.logger.error('Django API response error', {
                    status: error.response?.status,
                    url: error.config?.url,
                    message: error.message
                });
                return Promise.reject(error);
            }
        );
    }

    /**
     * Verify user with Django backend
     * @param {string} token - JWT token
     * @param {string} userId - User ID
     * @returns {Object} User information
     */
    async verifyUser(token, userId) {
        try {
            const cacheKey = `user:${userId}`;
            
            // Check cache first
            const cachedUser = await this.redis.get(cacheKey);
            if (cachedUser) {
                return JSON.parse(cachedUser);
            }

            const response = await this.apiClient.get('/api/auth/verify-user/', {
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                params: { user_id: userId }
            });

            const user = response.data;
            
            // Cache user data
            await this.redis.setex(cacheKey, AuthConfig.redis.ttl.userSession, JSON.stringify(user));
            
            return user;

        } catch (error) {
            this.logger.error('User verification failed', {
                userId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Check document access permissions
     * @param {string} token - JWT token
     * @param {string} userId - User ID
     * @param {string} documentId - Document ID
     * @returns {Object} Access information
     */
    async checkDocumentAccess(token, userId, documentId) {
        try {
            const cacheKey = `access:${userId}:${documentId}`;
            
            // Check cache first
            const cachedAccess = await this.redis.get(cacheKey);
            if (cachedAccess !== null) {
                return JSON.parse(cachedAccess);
            }

            const response = await this.apiClient.get(`/api/documents/${documentId}/check-access/`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                params: { user_id: userId }
            });

            const accessInfo = {
                hasAccess: response.data.has_access,
                permissions: response.data.permissions || ['read'],
                role: response.data.role || 'viewer'
            };
            
            // Cache access info
            await this.redis.setex(cacheKey, AuthConfig.redis.ttl.documentAccess, JSON.stringify(accessInfo));
            
            return accessInfo;

        } catch (error) {
            this.logger.error('Document access check failed', {
                userId,
                documentId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Get user permissions for a specific document
     * @param {string} token - JWT token
     * @param {string} userId - User ID
     * @param {string} documentId - Document ID
     * @returns {Array} User permissions
     */
    async getUserPermissions(token, userId, documentId) {
        try {
            const response = await this.apiClient.get('/api/auth/user-permissions/', {
                headers: {
                    'Authorization': `Bearer ${token}`
                },
                params: {
                    user_id: userId,
                    document_id: documentId
                }
            });

            return response.data.permissions || [];

        } catch (error) {
            this.logger.error('Failed to get user permissions', {
                userId,
                documentId,
                error: error.message
            });
            return [];
        }
    }

    /**
     * Notify Django about user connection
     * @param {Object} user - User object
     * @param {string} documentId - Document ID
     * @param {string} action - Action type (connect/disconnect)
     */
    async notifyUserActivity(user, documentId, action) {
        try {
            await this.apiClient.post('/api/collaboration/user-activity/', {
                user_id: user.id,
                document_id: documentId,
                action: action,
                timestamp: new Date().toISOString()
            }, {
                headers: {
                    'Authorization': `Bearer ${user.token}`
                }
            });

        } catch (error) {
            this.logger.warn('Failed to notify user activity', {
                userId: user.id,
                documentId,
                action,
                error: error.message
            });
            // Don't throw error as this is not critical
        }
    }

    /**
     * Get document metadata from Django
     * @param {string} token - JWT token
     * @param {string} documentId - Document ID
     * @returns {Object} Document metadata
     */
    async getDocumentMetadata(token, documentId) {
        try {
            const cacheKey = `doc:${documentId}`;
            
            // Check cache first
            const cachedDoc = await this.redis.get(cacheKey);
            if (cachedDoc) {
                return JSON.parse(cachedDoc);
            }

            const response = await this.apiClient.get(`/api/documents/${documentId}/`, {
                headers: {
                    'Authorization': `Bearer ${token}`
                }
            });

            const document = response.data;
            
            // Cache document metadata for 5 minutes
            await this.redis.setex(cacheKey, 300, JSON.stringify(document));
            
            return document;

        } catch (error) {
            this.logger.error('Failed to get document metadata', {
                documentId,
                error: error.message
            });
            throw error;
        }
    }

    /**
     * Refresh JWT token
     * @param {string} refreshToken - Refresh token
     * @returns {Object} New token information
     */
    async refreshToken(refreshToken) {
        try {
            const response = await this.apiClient.post('/api/auth/refresh/', {
                refresh: refreshToken
            });

            return {
                access: response.data.access,
                refresh: response.data.refresh,
                expiresIn: response.data.expires_in
            };

        } catch (error) {
            this.logger.error('Token refresh failed', error.message);
            throw error;
        }
    }

    /**
     * Invalidate cached user data
     * @param {string} userId - User ID
     */
    async invalidateUserCache(userId) {
        try {
            const pattern = `user:${userId}*`;
            const keys = await this.redis.keys(pattern);
            
            if (keys.length > 0) {
                await this.redis.del(...keys);
                this.logger.info('User cache invalidated', { userId, keysRemoved: keys.length });
            }

        } catch (error) {
            this.logger.error('Failed to invalidate user cache', {
                userId,
                error: error.message
            });
        }
    }

    /**
     * Health check for Django API
     * @returns {boolean} Whether Django API is healthy
     */
    async healthCheck() {
        try {
            const response = await this.apiClient.get('/health/', {
                timeout: 3000
            });
            
            return response.status === 200;

        } catch (error) {
            this.logger.error('Django health check failed', error.message);
            return false;
        }
    }

    /**
     * Close connections
     */
    async close() {
        if (this.redis) {
            await this.redis.quit();
        }
    }
}

module.exports = DjangoIntegrationService;
