// Enhanced Tiptap Bundle with Rich Text Features
// This file bundles all Tiptap extensions and YJS for collaborative editing

// YJS and WebSocket Provider
import * as Y from 'yjs';
import { WebsocketProvider } from 'y-websocket';

// Tiptap Core
import { Editor } from '@tiptap/core';
import StarterKit from '@tiptap/starter-kit';

// Collaboration Extensions
import Collaboration from '@tiptap/extension-collaboration';
import CollaborationCursor from '@tiptap/extension-collaboration-cursor';

// Additional Rich Text Extensions
import Underline from '@tiptap/extension-underline';
import TextAlign from '@tiptap/extension-text-align';
import Link from '@tiptap/extension-link';
import Color from '@tiptap/extension-color';
import Highlight from '@tiptap/extension-highlight';
import TextStyle from '@tiptap/extension-text-style';
import FontFamily from '@tiptap/extension-font-family';
import Superscript from '@tiptap/extension-superscript';
import Subscript from '@tiptap/extension-subscript';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';

// Export to global window object for browser usage
window.Y = Y;
window.WebsocketProvider = WebsocketProvider;
window.TiptapEditor = Editor;
window.TiptapStarterKit = StarterKit;
window.TiptapCollaboration = Collaboration;
window.TiptapCollaborationCursor = CollaborationCursor;

// Rich Text Extensions
window.TiptapUnderline = Underline;
window.TiptapTextAlign = TextAlign;
window.TiptapLink = Link;
window.TiptapColor = Color;
window.TiptapHighlight = Highlight;
window.TiptapTextStyle = TextStyle;
window.TiptapFontFamily = FontFamily;
window.TiptapSuperscript = Superscript;
window.TiptapSubscript = Subscript;
window.TiptapTable = Table;
window.TiptapTableRow = TableRow;
window.TiptapTableCell = TableCell;
window.TiptapTableHeader = TableHeader;

console.log('Enhanced Tiptap bundle with rich text features loaded successfully');
