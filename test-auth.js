#!/usr/bin/env node

/**
 * Test Authentication Script
 * This script demonstrates how to generate JWT tokens for testing
 * the WebSocket authentication without needing a full Django setup
 */

const jwt = require('jsonwebtoken');
const crypto = require('crypto');

// Configuration (should match your .env file)
const JWT_SECRET = 'your-super-secret-jwt-key-change-in-production';
const JWT_EXPIRES_IN = '24h';

// Mock user data
const mockUsers = [
    {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        isActive: true,
        isStaff: true,
        permissions: ['read', 'write', 'delete', 'share'],
        groups: ['administrators']
    },
    {
        id: 2,
        username: 'editor',
        email: '<EMAIL>',
        firstName: 'Editor',
        lastName: 'User',
        isActive: true,
        isStaff: false,
        permissions: ['read', 'write'],
        groups: ['editors']
    },
    {
        id: 3,
        username: 'viewer',
        email: '<EMAIL>',
        firstName: 'Viewer',
        lastName: 'User',
        isActive: true,
        isStaff: false,
        permissions: ['read'],
        groups: ['viewers']
    }
];

/**
 * Generate JWT token for a user
 */
function generateToken(user) {
    const payload = {
        userId: user.id,
        username: user.username,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        isActive: user.isActive,
        isStaff: user.isStaff,
        permissions: user.permissions,
        groups: user.groups,
        iat: Math.floor(Date.now() / 1000)
    };

    return jwt.sign(payload, JWT_SECRET, {
        algorithm: 'HS256',
        expiresIn: JWT_EXPIRES_IN
    });
}

/**
 * Verify JWT token
 */
function verifyToken(token) {
    try {
        return jwt.verify(token, JWT_SECRET);
    } catch (error) {
        console.error('Token verification failed:', error.message);
        return null;
    }
}

/**
 * Main function
 */
function main() {
    console.log('🔐 JWT Token Generator for WebSocket Authentication Testing\n');

    // Generate tokens for all mock users
    console.log('Generated JWT Tokens:\n');
    console.log('=' .repeat(80));

    mockUsers.forEach((user, index) => {
        const token = generateToken(user);
        
        console.log(`\n${index + 1}. User: ${user.username} (${user.email})`);
        console.log(`   Role: ${user.isStaff ? 'Staff' : 'Regular User'}`);
        console.log(`   Permissions: ${user.permissions.join(', ')}`);
        console.log(`   JWT Token:`);
        console.log(`   ${token}`);
        
        // Verify the token
        const decoded = verifyToken(token);
        if (decoded) {
            console.log(`   ✅ Token verified successfully`);
            console.log(`   📅 Expires: ${new Date(decoded.exp * 1000).toLocaleString()}`);
        } else {
            console.log(`   ❌ Token verification failed`);
        }
        
        // Generate test URL
        const testUrl = `http://localhost:3000/?token=${encodeURIComponent(token)}&user=${encodeURIComponent(user.username)}`;
        console.log(`   🔗 Test URL: ${testUrl}`);
        
        console.log('-'.repeat(80));
    });

    console.log('\n🚀 How to test:');
    console.log('1. Copy any JWT token from above');
    console.log('2. Visit http://localhost:3000/');
    console.log('3. Paste the token in the "JWT Token" field');
    console.log('4. Enter a username and click "Connect"');
    console.log('5. Or click on any test URL above for auto-login\n');

    console.log('💡 Pro tip: Open multiple browser tabs with different tokens to test multi-user collaboration!\n');

    // Generate a quick access token for immediate testing
    const quickToken = generateToken(mockUsers[0]);
    console.log('🎯 Quick Test Token (Admin User):');
    console.log(quickToken);
    console.log('\n🔗 Quick Test URL:');
    console.log(`http://localhost:3000/?token=${encodeURIComponent(quickToken)}&user=admin`);
}

// Run if called directly
if (require.main === module) {
    main();
}

module.exports = {
    generateToken,
    verifyToken,
    mockUsers
};
