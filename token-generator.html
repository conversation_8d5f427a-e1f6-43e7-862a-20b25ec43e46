<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JWT Token Generator - Testing</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
        }
        
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        input:focus {
            outline: none;
            border-color: #007bff;
        }
        
        button {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-bottom: 10px;
        }
        
        button:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
        }
        
        .token-display {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            word-break: break-all;
            font-family: 'Monaco', 'Consolas', monospace;
            font-size: 12px;
        }
        
        .success {
            color: #28a745;
            background: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 10px;
            border-radius: 6px;
            margin-top: 10px;
        }
        
        .warning {
            color: #856404;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .copy-btn {
            background: #28a745;
            padding: 8px 16px;
            font-size: 14px;
            margin-top: 10px;
            width: auto;
        }
        
        .test-btn {
            background: #17a2b8;
            padding: 8px 16px;
            font-size: 14px;
            margin-top: 10px;
            width: auto;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 JWT Token Generator (Testing Only)</h1>
        
        <div class="warning">
            <strong>⚠️ Testing Tool:</strong> This generates JWT tokens using the default secret key for testing purposes only. 
            In production, use proper Django authentication.
        </div>
        
        <form id="tokenForm">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" value="testuser" required>
            </div>
            
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="userId">User ID:</label>
                <input type="number" id="userId" name="userId" value="1" required>
            </div>
            
            <button type="submit">Generate Test JWT Token</button>
        </form>
        
        <div id="result"></div>
        
        <div id="tokenDisplay" class="token-display" style="display: none;">
            <strong>Generated JWT Token:</strong>
            <div id="tokenValue"></div>
            <button class="copy-btn" onclick="copyToken()">Copy Token</button>
            <button class="test-btn" onclick="openCollaborationEditor()">Test in Collaboration Editor</button>
        </div>
    </div>

    <!-- Include a simple JWT library for client-side token generation -->
    <script src="https://cdn.jsdelivr.net/npm/jsonwebtoken@9.0.2/index.js"></script>
    <script>
        // Simple JWT implementation for testing (not for production!)
        function base64UrlEncode(str) {
            return btoa(str)
                .replace(/\+/g, '-')
                .replace(/\//g, '_')
                .replace(/=/g, '');
        }
        
        function generateTestJWT(payload, secret = 'your-secret-key') {
            const header = {
                alg: 'HS256',
                typ: 'JWT'
            };
            
            const encodedHeader = base64UrlEncode(JSON.stringify(header));
            const encodedPayload = base64UrlEncode(JSON.stringify(payload));
            
            // For testing, we'll create a simple signature (not cryptographically secure)
            const signature = base64UrlEncode(`${encodedHeader}.${encodedPayload}.${secret}`);
            
            return `${encodedHeader}.${encodedPayload}.${signature}`;
        }
        
        document.getElementById('tokenForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const email = document.getElementById('email').value;
            const userId = parseInt(document.getElementById('userId').value);
            const resultDiv = document.getElementById('result');
            
            try {
                // Create JWT payload
                const now = Math.floor(Date.now() / 1000);
                const payload = {
                    user_id: userId,
                    username: username,
                    email: email,
                    permissions: ['read', 'write'],
                    exp: now + (24 * 60 * 60), // 24 hours
                    iat: now
                };
                
                // Generate token
                const token = generateTestJWT(payload);
                
                // Display success message
                resultDiv.innerHTML = '<div class="success">✅ Test JWT token generated successfully!</div>';
                
                // Display token
                document.getElementById('tokenValue').textContent = token;
                document.getElementById('tokenDisplay').style.display = 'block';
                
                // Store token for later use
                localStorage.setItem('test_jwt_token', token);
                localStorage.setItem('test_user_info', JSON.stringify({
                    username: username,
                    email: email,
                    user_id: userId
                }));
                
            } catch (error) {
                console.error('Token generation error:', error);
                resultDiv.innerHTML = `<div class="error">❌ Token generation failed: ${error.message}</div>`;
                document.getElementById('tokenDisplay').style.display = 'none';
            }
        });
        
        function copyToken() {
            const tokenValue = document.getElementById('tokenValue').textContent;
            navigator.clipboard.writeText(tokenValue).then(() => {
                alert('Token copied to clipboard!');
            }).catch(err => {
                console.error('Failed to copy token:', err);
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = tokenValue;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                alert('Token copied to clipboard!');
            });
        }
        
        function openCollaborationEditor() {
            const token = document.getElementById('tokenValue').textContent;
            const userInfo = JSON.parse(localStorage.getItem('test_user_info') || '{}');
            
            // Open collaboration editor with pre-filled token
            const editorUrl = `http://localhost:3000/?token=${encodeURIComponent(token)}&user=${encodeURIComponent(userInfo.username || 'testuser')}`;
            window.open(editorUrl, '_blank');
        }
        
        // Check if we have a stored token
        window.addEventListener('load', () => {
            const storedToken = localStorage.getItem('test_jwt_token');
            if (storedToken) {
                document.getElementById('tokenValue').textContent = storedToken;
                document.getElementById('tokenDisplay').style.display = 'block';
                document.getElementById('result').innerHTML = '<div class="success">✅ Using stored test token</div>';
            }
        });
    </script>
</body>
</html>
